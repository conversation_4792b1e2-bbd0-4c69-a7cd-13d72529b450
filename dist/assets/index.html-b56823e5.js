import"./modulepreload-polyfill-3cfb730f.js";import{c as h,j as e,r as i}from"./client-25e9187c.js";const o={fontSize:16,lineHeight:1.6,backgroundColor:"#ffffff",textColor:"#333333",fontFamily:"system-ui",autoScroll:!1,scrollSpeed:50,enableKeyboardShortcuts:!0},m=()=>{const[t,s]=i.useState(o),[l,a]=i.useState(!1);i.useEffect(()=>{chrome.storage.sync.get("readerSettings",n=>{n.readerSettings&&s({...o,...n.readerSettings})})},[]);const c=()=>{chrome.storage.sync.set({readerSettings:t},()=>{a(!0),setTimeout(()=>a(!1),2e3)})},d=()=>{s(o)};return e.jsxs("div",{className:"options-container",children:[e.jsxs("header",{className:"header",children:[e.jsx("h1",{children:"小说阅读助手 - 设置"}),e.jsx("p",{children:"自定义您的阅读体验"})]}),e.jsxs("main",{className:"main-content",children:[e.jsxs("section",{className:"settings-section",children:[e.jsx("h2",{children:"阅读样式"}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{htmlFor:"fontSize",children:"字体大小"}),e.jsx("input",{type:"range",id:"fontSize",min:"12",max:"24",value:t.fontSize,onChange:n=>s({...t,fontSize:parseInt(n.target.value)})}),e.jsxs("span",{children:[t.fontSize,"px"]})]}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{htmlFor:"lineHeight",children:"行高"}),e.jsx("input",{type:"range",id:"lineHeight",min:"1.2",max:"2.0",step:"0.1",value:t.lineHeight,onChange:n=>s({...t,lineHeight:parseFloat(n.target.value)})}),e.jsx("span",{children:t.lineHeight})]}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{htmlFor:"fontFamily",children:"字体"}),e.jsxs("select",{id:"fontFamily",value:t.fontFamily,onChange:n=>s({...t,fontFamily:n.target.value}),children:[e.jsx("option",{value:"system-ui",children:"系统默认"}),e.jsx("option",{value:"serif",children:"宋体"}),e.jsx("option",{value:"sans-serif",children:"黑体"}),e.jsx("option",{value:"monospace",children:"等宽字体"})]})]}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{htmlFor:"backgroundColor",children:"背景颜色"}),e.jsx("input",{type:"color",id:"backgroundColor",value:t.backgroundColor,onChange:n=>s({...t,backgroundColor:n.target.value})})]}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{htmlFor:"textColor",children:"文字颜色"}),e.jsx("input",{type:"color",id:"textColor",value:t.textColor,onChange:n=>s({...t,textColor:n.target.value})})]})]}),e.jsxs("section",{className:"settings-section",children:[e.jsx("h2",{children:"阅读功能"}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{htmlFor:"autoScroll",children:"自动滚动"}),e.jsx("input",{type:"checkbox",id:"autoScroll",checked:t.autoScroll,onChange:n=>s({...t,autoScroll:n.target.checked})})]}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{htmlFor:"scrollSpeed",children:"滚动速度"}),e.jsx("input",{type:"range",id:"scrollSpeed",min:"10",max:"100",value:t.scrollSpeed,onChange:n=>s({...t,scrollSpeed:parseInt(n.target.value)})}),e.jsx("span",{children:t.scrollSpeed})]}),e.jsxs("div",{className:"setting-item",children:[e.jsx("label",{htmlFor:"enableKeyboardShortcuts",children:"启用键盘快捷键"}),e.jsx("input",{type:"checkbox",id:"enableKeyboardShortcuts",checked:t.enableKeyboardShortcuts,onChange:n=>s({...t,enableKeyboardShortcuts:n.target.checked})})]})]}),e.jsxs("div",{className:"actions",children:[e.jsx("button",{onClick:c,className:"save-btn",children:l?"已保存!":"保存设置"}),e.jsx("button",{onClick:d,className:"reset-btn",children:"重置为默认"})]})]})]})},r=document.getElementById("root");r&&h(r).render(e.jsx(m,{}));
