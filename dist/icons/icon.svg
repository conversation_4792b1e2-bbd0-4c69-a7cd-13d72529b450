<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 书本背景 -->
  <rect x="20" y="16" width="88" height="96" rx="8" ry="8" fill="url(#bookGradient)" />
  
  <!-- 书本边框 -->
  <rect x="20" y="16" width="88" height="96" rx="8" ry="8" fill="none" stroke="#4a5568" stroke-width="2" />
  
  <!-- 书脊 -->
  <rect x="20" y="16" width="12" height="96" rx="8" ry="8" fill="#4a5568" />
  
  <!-- 页面线条 -->
  <line x1="40" y1="32" x2="92" y2="32" stroke="white" stroke-width="2" opacity="0.8" />
  <line x1="40" y1="44" x2="92" y2="44" stroke="white" stroke-width="2" opacity="0.8" />
  <line x1="40" y1="56" x2="92" y2="56" stroke="white" stroke-width="2" opacity="0.8" />
  <line x1="40" y1="68" x2="80" y2="68" stroke="white" stroke-width="2" opacity="0.8" />
  <line x1="40" y1="80" x2="88" y2="80" stroke="white" stroke-width="2" opacity="0.8" />
  <line x1="40" y1="92" x2="75" y2="92" stroke="white" stroke-width="2" opacity="0.8" />
  
  <!-- 书签 -->
  <rect x="24" y="12" width="4" height="20" fill="#ffd700" />
  <polygon points="24,32 28,32 26,28" fill="#ffd700" />
</svg>
