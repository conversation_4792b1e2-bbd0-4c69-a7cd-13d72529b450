chrome.runtime.onInstalled.addListener(e=>{console.log("小说阅读助手已安装/更新"),e.reason==="install"&&(chrome.tabs.create({url:chrome.runtime.getURL("welcome.html")}),chrome.storage.sync.set({settings:{fontSize:16,fontFamily:"Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif",lineHeight:1.8,theme:"light",pageWidth:800,autoScroll:!1,scrollSpeed:50},enabledSites:["jjwxc.net","qidian.com","zongheng.com"],showFloatingButton:!0,keyboardShortcuts:!0}))});chrome.runtime.onMessage.addListener((e,t,r)=>{switch(e.type){case"GET_SETTINGS":return chrome.storage.sync.get(["settings"],o=>{r({settings:o.settings})}),!0;case"SAVE_SETTINGS":return chrome.storage.sync.set({settings:e.settings},()=>{r({success:!0}),chrome.tabs.query({},o=>{o.forEach(s=>{s.id&&chrome.tabs.sendMessage(s.id,{type:"SETTINGS_UPDATED",settings:e.settings}).catch(()=>{})})})}),!0;case"ADD_BOOKMARK":return a(e.bookmark,r),!0;case"GET_BOOKMARKS":return n(r),!0;case"REMOVE_BOOKMARK":return i(e.bookmarkId,r),!0;case"GET_READING_HISTORY":return l(r),!0;case"CLEAR_READING_HISTORY":return h(r),!0;case"REPORT_ERROR":console.error("Content Script Error:",e.error);break}});chrome.tabs.onUpdated.addListener((e,t,r)=>{t.status==="complete"&&r.url&&(["jjwxc.net","qidian.com","zongheng.com"].some(c=>r.url.includes(c))?(chrome.action.setIcon({tabId:e,path:{16:"icons/icon16.svg",32:"icons/icon32.svg",48:"icons/icon48.svg",128:"icons/icon128.svg"}}),chrome.action.setTitle({tabId:e,title:"小说阅读助手 - 点击启用阅读模式"})):(chrome.action.setIcon({tabId:e,path:{16:"icons/icon16.svg",32:"icons/icon32.svg",48:"icons/icon48.svg",128:"icons/icon128.svg"}}),chrome.action.setTitle({tabId:e,title:"小说阅读助手 - 不支持当前网站"})))});chrome.commands.onCommand.addListener((e,t)=>{e==="toggle-reader-mode"&&t.id&&chrome.tabs.sendMessage(t.id,{type:"TOGGLE_READER_MODE"})});async function a(e,t){try{const o=(await chrome.storage.sync.get(["bookmarks"])).bookmarks||[];if(o.some(c=>c.chapterUrl===e.chapterUrl)){t({success:!1,error:"书签已存在"});return}o.unshift({...e,id:m(),createdAt:Date.now()}),o.length>100&&o.splice(100),await chrome.storage.sync.set({bookmarks:o}),t({success:!0})}catch(r){console.error("添加书签失败:",r),t({success:!1,error:"添加书签失败"})}}async function n(e){try{const t=await chrome.storage.sync.get(["bookmarks"]);e({bookmarks:t.bookmarks||[]})}catch(t){console.error("获取书签失败:",t),e({bookmarks:[]})}}async function i(e,t){try{const s=((await chrome.storage.sync.get(["bookmarks"])).bookmarks||[]).filter(c=>c.id!==e);await chrome.storage.sync.set({bookmarks:s}),t({success:!0})}catch(r){console.error("删除书签失败:",r),t({success:!1,error:"删除书签失败"})}}async function l(e){try{const t=await chrome.storage.local.get(["readingHistory"]);e({history:t.readingHistory||[]})}catch(t){console.error("获取历史记录失败:",t),e({history:[]})}}async function h(e){try{await chrome.storage.local.set({readingHistory:[]}),e({success:!0})}catch(t){console.error("清空历史记录失败:",t),e({success:!1,error:"清空历史记录失败"})}}function m(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}chrome.storage.onChanged.addListener((e,t)=>{t==="sync"&&e.settings&&chrome.tabs.query({},r=>{r.forEach(o=>{o.id&&chrome.tabs.sendMessage(o.id,{type:"SETTINGS_UPDATED",settings:e.settings.newValue}).catch(()=>{})})})});chrome.alarms.create("cleanup",{periodInMinutes:60*24});chrome.alarms.onAlarm.addListener(async e=>{if(e.name==="cleanup")try{const r=(await chrome.storage.local.get(["readingHistory"])).readingHistory||[],o=Date.now()-30*24*60*60*1e3,s=r.filter(c=>c.visitedAt>o);s.length!==r.length&&(await chrome.storage.local.set({readingHistory:s}),console.log(`清理了 ${r.length-s.length} 条过期历史记录`))}catch(t){console.error("清理数据失败:",t)}});chrome.runtime.setUninstallURL("https://forms.gle/feedback-uninstall");console.log("小说阅读助手后台脚本已启动");
