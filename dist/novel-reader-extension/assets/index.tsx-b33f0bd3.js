import{r as j,g as J,R as K,j as e,c as P}from"./client-25e9187c.js";const F=t=>{let o;const n=new Set,r=(u,y)=>{const x=typeof u=="function"?u(o):u;if(!Object.is(x,o)){const f=o;o=y??(typeof x!="object"||x===null)?x:Object.assign({},o,x),n.forEach(_=>_(o,f))}},a=()=>o,g={setState:r,getState:a,getInitialState:()=>v,subscribe:u=>(n.add(u),()=>n.delete(u)),destroy:()=>{n.clear()}},v=o=t(r,a,g);return g},Y=t=>t?F(t):F;var V={exports:{}},$={},z={exports:{}},U={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var k=j;function X(t,o){return t===o&&(t!==0||1/t===1/o)||t!==t&&o!==o}var Q=typeof Object.is=="function"?Object.is:X,ee=k.useState,te=k.useEffect,ne=k.useLayoutEffect,oe=k.useDebugValue;function se(t,o){var n=o(),r=ee({inst:{value:n,getSnapshot:o}}),a=r[0].inst,i=r[1];return ne(function(){a.value=n,a.getSnapshot=o,I(a)&&i({inst:a})},[t,n,o]),te(function(){return I(a)&&i({inst:a}),t(function(){I(a)&&i({inst:a})})},[t]),oe(n),n}function I(t){var o=t.getSnapshot;t=t.value;try{var n=o();return!Q(t,n)}catch{return!0}}function re(t,o){return o()}var ie=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?re:se;U.useSyncExternalStore=k.useSyncExternalStore!==void 0?k.useSyncExternalStore:ie;z.exports=U;var ae=z.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var T=j,le=ae;function ce(t,o){return t===o&&(t!==0||1/t===1/o)||t!==t&&o!==o}var de=typeof Object.is=="function"?Object.is:ce,ue=le.useSyncExternalStore,he=T.useRef,me=T.useEffect,pe=T.useMemo,fe=T.useDebugValue;$.useSyncExternalStoreWithSelector=function(t,o,n,r,a){var i=he(null);if(i.current===null){var l={hasValue:!1,value:null};i.current=l}else l=i.current;i=pe(function(){function g(f){if(!v){if(v=!0,u=f,f=r(f),a!==void 0&&l.hasValue){var _=l.value;if(a(_,f))return y=_}return y=f}if(_=y,de(u,f))return _;var c=r(f);return a!==void 0&&a(_,c)?(u=f,_):(u=f,y=c)}var v=!1,u,y,x=n===void 0?null:n;return[function(){return g(o())},x===null?void 0:function(){return g(x())}]},[o,n,r,a]);var p=ue(t,i[0],i[1]);return me(function(){l.hasValue=!0,l.value=p},[p]),fe(p),p};V.exports=$;var ve=V.exports;const ge=J(ve),{useDebugValue:xe}=K,{useSyncExternalStoreWithSelector:_e}=ge;const ye=t=>t;function Se(t,o=ye,n){const r=_e(t.subscribe,t.getState,t.getServerState||t.getInitialState,o,n);return xe(r),r}const O=t=>{const o=typeof t=="function"?Y(t):t,n=(r,a)=>Se(o,r,a);return Object.assign(n,o),n},q=t=>t?O(t):O,H=new Map,R=t=>{const o=H.get(t);return o?Object.fromEntries(Object.entries(o.stores).map(([n,r])=>[n,r.getState()])):{}},be=(t,o,n)=>{if(t===void 0)return{type:"untracked",connection:o.connect(n)};const r=H.get(n.name);if(r)return{type:"tracked",store:t,...r};const a={connection:o.connect(n),stores:{}};return H.set(n.name,a),{type:"tracked",store:t,...a}},Ce=(t,o={})=>(n,r,a)=>{const{enabled:i,anonymousActionType:l,store:p,...g}=o;let v;try{v=(i??!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!v)return t(n,r,a);const{connection:u,...y}=be(p,v,g);let x=!0;a.setState=(c,h,d)=>{const m=n(c,h);if(!x)return m;const S=d===void 0?{type:l||"anonymous"}:typeof d=="string"?{type:d}:d;return p===void 0?(u==null||u.send(S,r()),m):(u==null||u.send({...S,type:`${p}/${S.type}`},{...R(g.name),[p]:a.getState()}),m)};const f=(...c)=>{const h=x;x=!1,n(...c),x=h},_=t(a.setState,r,a);if(y.type==="untracked"?u==null||u.init(_):(y.stores[y.store]=a,u==null||u.init(Object.fromEntries(Object.entries(y.stores).map(([c,h])=>[c,c===y.store?_:h.getState()])))),a.dispatchFromDevtools&&typeof a.dispatch=="function"){let c=!1;const h=a.dispatch;a.dispatch=(...d)=>{h(...d)}}return u.subscribe(c=>{var h;switch(c.type){case"ACTION":if(typeof c.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return M(c.payload,d=>{if(d.type==="__setState"){if(p===void 0){f(d.state);return}Object.keys(d.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const m=d.state[p];if(m==null)return;JSON.stringify(a.getState())!==JSON.stringify(m)&&f(m);return}a.dispatchFromDevtools&&typeof a.dispatch=="function"&&a.dispatch(d)});case"DISPATCH":switch(c.payload.type){case"RESET":return f(_),p===void 0?u==null?void 0:u.init(a.getState()):u==null?void 0:u.init(R(g.name));case"COMMIT":if(p===void 0){u==null||u.init(a.getState());return}return u==null?void 0:u.init(R(g.name));case"ROLLBACK":return M(c.state,d=>{if(p===void 0){f(d),u==null||u.init(a.getState());return}f(d[p]),u==null||u.init(R(g.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return M(c.state,d=>{if(p===void 0){f(d);return}JSON.stringify(a.getState())!==JSON.stringify(d[p])&&f(d[p])});case"IMPORT_STATE":{const{nextLiftedState:d}=c.payload,m=(h=d.computedStates.slice(-1)[0])==null?void 0:h.state;if(!m)return;f(p===void 0?m:m[p]),u==null||u.send(null,d);return}case"PAUSE_RECORDING":return x=!x}return}}),_},je=Ce,M=(t,o)=>{let n;try{n=JSON.parse(t)}catch(r){console.error("[zustand devtools middleware] Could not parse the received json",r)}n!==void 0&&o(n)};function we(t,o){let n;try{n=t()}catch{return}return{getItem:a=>{var i;const l=g=>g===null?null:JSON.parse(g,o==null?void 0:o.reviver),p=(i=n.getItem(a))!=null?i:null;return p instanceof Promise?p.then(l):l(p)},setItem:(a,i)=>n.setItem(a,JSON.stringify(i,o==null?void 0:o.replacer)),removeItem:a=>n.removeItem(a)}}const E=t=>o=>{try{const n=t(o);return n instanceof Promise?n:{then(r){return E(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return E(r)(n)}}}},ke=(t,o)=>(n,r,a)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:h=>h,version:0,merge:(h,d)=>({...d,...h}),...o},l=!1;const p=new Set,g=new Set;let v;try{v=i.getStorage()}catch{}if(!v)return t((...h)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...h)},r,a);const u=E(i.serialize),y=()=>{const h=i.partialize({...r()});let d;const m=u({state:h,version:i.version}).then(S=>v.setItem(i.name,S)).catch(S=>{d=S});if(d)throw d;return m},x=a.setState;a.setState=(h,d)=>{x(h,d),y()};const f=t((...h)=>{n(...h),y()},r,a);let _;const c=()=>{var h;if(!v)return;l=!1,p.forEach(m=>m(r()));const d=((h=i.onRehydrateStorage)==null?void 0:h.call(i,r()))||void 0;return E(v.getItem.bind(v))(i.name).then(m=>{if(m)return i.deserialize(m)}).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return i.migrate(m.state,m.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return m.state}).then(m=>{var S;return _=i.merge(m,(S=r())!=null?S:f),n(_,!0),y()}).then(()=>{d==null||d(_,void 0),l=!0,g.forEach(m=>m(_))}).catch(m=>{d==null||d(void 0,m)})};return a.persist={setOptions:h=>{i={...i,...h},h.getStorage&&(v=h.getStorage())},clearStorage:()=>{v==null||v.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>c(),hasHydrated:()=>l,onHydrate:h=>(p.add(h),()=>{p.delete(h)}),onFinishHydration:h=>(g.add(h),()=>{g.delete(h)})},c(),_||f},Ne=(t,o)=>(n,r,a)=>{let i={storage:we(()=>localStorage),partialize:c=>c,version:0,merge:(c,h)=>({...h,...c}),...o},l=!1;const p=new Set,g=new Set;let v=i.storage;if(!v)return t((...c)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...c)},r,a);const u=()=>{const c=i.partialize({...r()});return v.setItem(i.name,{state:c,version:i.version})},y=a.setState;a.setState=(c,h)=>{y(c,h),u()};const x=t((...c)=>{n(...c),u()},r,a);a.getInitialState=()=>x;let f;const _=()=>{var c,h;if(!v)return;l=!1,p.forEach(m=>{var S;return m((S=r())!=null?S:x)});const d=((h=i.onRehydrateStorage)==null?void 0:h.call(i,(c=r())!=null?c:x))||void 0;return E(v.getItem.bind(v))(i.name).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return[!0,i.migrate(m.state,m.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,m.state];return[!1,void 0]}).then(m=>{var S;const[N,B]=m;if(f=i.merge(B,(S=r())!=null?S:x),n(f,!0),N)return u()}).then(()=>{d==null||d(f,void 0),f=r(),l=!0,g.forEach(m=>m(f))}).catch(m=>{d==null||d(void 0,m)})};return a.persist={setOptions:c=>{i={...i,...c},c.storage&&(v=c.storage)},clearStorage:()=>{v==null||v.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>_(),hasHydrated:()=>l,onHydrate:c=>(p.add(c),()=>{p.delete(c)}),onFinishHydration:c=>(g.add(c),()=>{g.delete(c)})},i.skipHydration||_(),f||x},Be=(t,o)=>"getStorage"in o||"serialize"in o||"deserialize"in o?ke(t,o):Ne(t,o),G=Be,w=q()(je(G((t,o)=>({isReaderMode:!1,currentChapter:null,readingPosition:0,showSettings:!1,showBookmarks:!1,settings:{fontSize:16,fontFamily:"Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif",lineHeight:1.8,theme:"light",pageWidth:800,autoScroll:!1,scrollSpeed:50},enterReaderMode:n=>{t({isReaderMode:!0,currentChapter:n,showSettings:!1,showBookmarks:!1})},exitReaderMode:()=>{t({isReaderMode:!1,showSettings:!1,showBookmarks:!1})},updateSettings:n=>{t(r=>({settings:{...r.settings,...n}}))},setCurrentChapter:n=>{t({currentChapter:n})},updateReadingPosition:n=>{t({readingPosition:n})},toggleSettings:()=>{t(n=>({showSettings:!n.showSettings,showBookmarks:!1}))},toggleBookmarks:()=>{t(n=>({showBookmarks:!n.showBookmarks,showSettings:!1}))},loadChapter:n=>{t({currentChapter:n})}}),{name:"novel-reader-settings",partialize:t=>({settings:t.settings})}))),A=q()(G((t,o)=>({history:[],currentHistoryIndex:-1,bookmarks:[],favorites:[],currentSession:[],currentSessionIndex:-1,loadingNext:!1,loadingPrev:!1,addToHistory:n=>{t(r=>{const a=r.history.findIndex(l=>l.id===n.id||l.url===n.url);let i=[...r.history];return a>=0?i[a]={...i[a],...n,timestamp:Date.now()}:(i.unshift({...n,timestamp:Date.now()}),i.length>100&&(i=i.slice(0,100))),{history:i,currentHistoryIndex:0}})},removeFromHistory:n=>{t(r=>({history:r.history.filter(a=>a.id!==n)}))},clearHistory:()=>{t({history:[],currentHistoryIndex:-1})},addBookmark:n=>{t(r=>r.bookmarks.some(i=>i.id===n.id)?r:{bookmarks:[{...n,timestamp:Date.now()},...r.bookmarks]})},removeBookmark:n=>{t(r=>({bookmarks:r.bookmarks.filter(a=>a.id!==n)}))},addToFavorites:n=>{t(r=>r.favorites.some(i=>i.id===n.id)?r:{favorites:[{...n,timestamp:Date.now()},...r.favorites]})},removeFromFavorites:n=>{t(r=>({favorites:r.favorites.filter(a=>a.id!==n)}))},startNewSession:n=>{t({currentSession:[n],currentSessionIndex:0})},addToCurrentSession:n=>{t(r=>r.currentSession.some(i=>i.id===n.id)?{currentSessionIndex:r.currentSession.findIndex(l=>l.id===n.id)}:{currentSession:[...r.currentSession,n],currentSessionIndex:r.currentSession.length})},navigateInSession:n=>{const r=o(),{currentSession:a,currentSessionIndex:i}=r;if(a.length===0)return null;let l=i;if(n==="prev"&&i>0)l=i-1;else if(n==="next"&&i<a.length-1)l=i+1;else return null;return t({currentSessionIndex:l}),a[l]},navigateToNext:async()=>Promise.resolve(),navigateToPrev:async()=>Promise.resolve(),updateReadingProgress:(n,r)=>{t(a=>{const i=l=>l.map(p=>p.id===n?{...p,readingProgress:Math.max(0,Math.min(100,r))}:p);return{history:i(a.history),bookmarks:i(a.bookmarks),favorites:i(a.favorites),currentSession:i(a.currentSession)}})},searchChapters:n=>{const r=o();return[...r.history,...r.bookmarks,...r.favorites].filter((l,p,g)=>g.findIndex(v=>v.id===l.id)===p).filter(l=>l.title.toLowerCase().includes(n.toLowerCase())||l.content.toLowerCase().includes(n.toLowerCase()))},getChaptersBySite:n=>o().history.filter(a=>a.site===n),getRecentChapters:(n=10)=>o().history.sort((a,i)=>i.timestamp-a.timestamp).slice(0,n)}),{name:"novel-reader-chapters",version:1})),Le="nr__readerContainer__Al1-y",Ee="nr__progressBar__vub5d",Re="nr__progressFill__av5Op",Te="nr__readerHeader__9ynG9",Ie="nr__headerLeft__v3xCr",Me="nr__headerRight__LPq38",He="nr__chapterTitle__DD2NQ",Ae="nr__exitButton__ZODXY",De="nr__headerButton__a9D8y",Pe="nr__active__amugk",Fe="nr__readerMain__GGPRo",Oe="nr__readerContent__25cl1",Ve="nr__chapterContent__C2poS",$e="nr__paywallNotice__2ihjV",ze="nr__paywallIcon__dGA--",Ue="nr__paywallButton__rW5Jx",qe="nr__additionalContent__YJutg",Ge="nr__authorInfo__leguK",We="nr__commentsSection__UH1Zf",Ze="nr__chapterNavigation__Bb-2R",Je="nr__navButton__RDqKX",Ke="nr__primary__QYJq1",Ye="nr__navControls__yVxW-",Xe="nr__controlButton__7ZH5N",Qe="nr__floatingControls__hFOqS",et="nr__floatingButton__VVnmA",tt="nr__sidePanel__jF4jf",nt="nr__open__Sbyzp",ot="nr__panelHeader__HMhLr",st="nr__panelTitle__qjSdR",rt="nr__panelCloseButton__DH0wO",it="nr__panelContent__Kraiw",at="nr__settingGroup__QkUdj",lt="nr__settingLabel__UrSxl",ct="nr__settingSlider__Vejt2",dt="nr__settingSelect__IuPbu",ut="nr__themeButtons__PYpQP",ht="nr__themeButton__GDZtD",mt="nr__bookmarkList__a93Pf",pt="nr__bookmarkItem__wZ5-s",ft="nr__bookmarkTitle__i2rv-",vt="nr__bookmarkTime__oFY1A",gt="nr__notification__oAUNQ",xt="nr__lightTheme__L5cIw",_t="nr__darkTheme__Bmiml",yt="nr__sepiaTheme__Ber-L",St="nr__fadeIn__b-29o",s={readerContainer:Le,progressBar:Ee,progressFill:Re,readerHeader:Te,headerLeft:Ie,headerRight:Me,chapterTitle:He,exitButton:Ae,headerButton:De,active:Pe,readerMain:Fe,readerContent:Oe,chapterContent:Ve,paywallNotice:$e,paywallIcon:ze,paywallButton:Ue,additionalContent:qe,authorInfo:Ge,commentsSection:We,chapterNavigation:Ze,navButton:Je,primary:Ke,navControls:Ye,controlButton:Xe,floatingControls:Qe,floatingButton:et,sidePanel:tt,open:nt,panelHeader:ot,panelTitle:st,panelCloseButton:rt,panelContent:it,settingGroup:at,settingLabel:lt,settingSlider:ct,settingSelect:dt,themeButtons:ut,themeButton:ht,bookmarkList:mt,bookmarkItem:pt,bookmarkTitle:ft,bookmarkTime:vt,notification:gt,lightTheme:xt,darkTheme:_t,sepiaTheme:yt,fadeIn:St},bt=()=>{const{settings:t,showSettings:o,updateSettings:n,toggleSettings:r}=w(),a=[{value:"Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif",label:"微软雅黑"},{value:"SimSun, serif",label:"宋体"},{value:"KaiTi, serif",label:"楷体"},{value:"SimHei, sans-serif",label:"黑体"},{value:"-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif",label:"系统字体"}],i=[{value:"light",label:"日间",icon:"☀️"},{value:"dark",label:"夜间",icon:"🌙"},{value:"sepia",label:"护眼",icon:"📖"}];return e.jsxs("div",{className:`${s.sidePanel} ${o?s.open:""}`,children:[e.jsxs("div",{className:s.panelHeader,children:[e.jsx("h3",{className:s.panelTitle,children:"阅读设置"}),e.jsx("button",{className:s.panelCloseButton,onClick:r,children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})})]}),e.jsxs("div",{className:s.panelContent,children:[e.jsxs("div",{className:s.settingGroup,children:[e.jsx("label",{className:s.settingLabel,children:"阅读主题"}),e.jsx("div",{className:s.themeButtons,children:i.map(l=>e.jsxs("button",{className:`${s.themeButton} ${t.theme===l.value?s.active:""}`,onClick:()=>n({theme:l.value}),children:[e.jsx("span",{style:{marginRight:"4px"},children:l.icon}),l.label]},l.value))})]}),e.jsxs("div",{className:s.settingGroup,children:[e.jsxs("label",{className:s.settingLabel,children:["字体大小 (",t.fontSize,"px)"]}),e.jsx("input",{type:"range",min:"12",max:"28",step:"1",value:t.fontSize,onChange:l=>n({fontSize:parseInt(l.target.value)}),className:s.settingSlider}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",color:"var(--label-color)",marginTop:"4px"},children:[e.jsx("span",{children:"12px"}),e.jsx("span",{children:"20px"}),e.jsx("span",{children:"28px"})]})]}),e.jsxs("div",{className:s.settingGroup,children:[e.jsx("label",{className:s.settingLabel,children:"字体类型"}),e.jsx("select",{value:t.fontFamily,onChange:l=>n({fontFamily:l.target.value}),className:s.settingSelect,children:a.map(l=>e.jsx("option",{value:l.value,children:l.label},l.value))})]}),e.jsxs("div",{className:s.settingGroup,children:[e.jsxs("label",{className:s.settingLabel,children:["行间距 (",t.lineHeight,")"]}),e.jsx("input",{type:"range",min:"1.2",max:"2.5",step:"0.1",value:t.lineHeight,onChange:l=>n({lineHeight:parseFloat(l.target.value)}),className:s.settingSlider}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",color:"var(--label-color)",marginTop:"4px"},children:[e.jsx("span",{children:"紧密"}),e.jsx("span",{children:"适中"}),e.jsx("span",{children:"宽松"})]})]}),e.jsxs("div",{className:s.settingGroup,children:[e.jsxs("label",{className:s.settingLabel,children:["页面宽度 (",t.pageWidth,"px)"]}),e.jsx("input",{type:"range",min:"600",max:"1200",step:"50",value:t.pageWidth,onChange:l=>n({pageWidth:parseInt(l.target.value)}),className:s.settingSlider}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",color:"var(--label-color)",marginTop:"4px"},children:[e.jsx("span",{children:"窄屏"}),e.jsx("span",{children:"适中"}),e.jsx("span",{children:"宽屏"})]})]}),e.jsxs("div",{className:s.settingGroup,children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"8px"},children:[e.jsx("label",{className:s.settingLabel,style:{margin:0},children:"自动滚动"}),e.jsxs("label",{className:s.toggleSwitch,children:[e.jsx("input",{type:"checkbox",checked:t.autoScroll,onChange:l=>n({autoScroll:l.target.checked})}),e.jsx("span",{className:s.toggleSlider})]})]}),t.autoScroll&&e.jsxs(e.Fragment,{children:[e.jsxs("label",{className:s.settingLabel,children:["滚动速度 (",t.scrollSpeed,")"]}),e.jsx("input",{type:"range",min:"10",max:"100",step:"5",value:t.scrollSpeed,onChange:l=>n({scrollSpeed:parseInt(l.target.value)}),className:s.settingSlider}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",fontSize:"12px",color:"var(--label-color)",marginTop:"4px"},children:[e.jsx("span",{children:"慢"}),e.jsx("span",{children:"适中"}),e.jsx("span",{children:"快"})]})]})]}),e.jsxs("div",{className:s.settingGroup,children:[e.jsx("label",{className:s.settingLabel,children:"快捷键"}),e.jsxs("div",{className:s.shortcutList,children:[e.jsxs("div",{className:s.shortcutItem,children:[e.jsx("span",{className:s.shortcutKey,children:"ESC"}),e.jsx("span",{className:s.shortcutDesc,children:"退出阅读模式"})]}),e.jsxs("div",{className:s.shortcutItem,children:[e.jsx("span",{className:s.shortcutKey,children:"← →"}),e.jsx("span",{className:s.shortcutDesc,children:"上一章/下一章"})]}),e.jsxs("div",{className:s.shortcutItem,children:[e.jsx("span",{className:s.shortcutKey,children:"空格"}),e.jsx("span",{className:s.shortcutDesc,children:"下一章"})]}),e.jsxs("div",{className:s.shortcutItem,children:[e.jsx("span",{className:s.shortcutKey,children:"Ctrl+B"}),e.jsx("span",{className:s.shortcutDesc,children:"添加书签"})]}),e.jsxs("div",{className:s.shortcutItem,children:[e.jsx("span",{className:s.shortcutKey,children:"Ctrl+S"}),e.jsx("span",{className:s.shortcutDesc,children:"打开设置"})]})]})]}),e.jsx("div",{className:s.settingGroup,children:e.jsx("button",{className:s.resetButton,onClick:()=>{n({fontSize:16,fontFamily:"Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif",lineHeight:1.8,theme:"light",pageWidth:800,autoScroll:!1,scrollSpeed:50})},children:"重置为默认设置"})})]})]})},Ct=()=>{const{bookmarks:t,favorites:o,history:n,removeBookmark:r,removeFromFavorites:a,addToFavorites:i,addBookmark:l}=A(),{loadChapter:p,toggleBookmarks:g}=w(),[v,u]=j.useState("bookmarks"),y=c=>{p(c),g()},x=c=>{o.some(d=>d.id===c.id)?a(c.id):i(c)},f=c=>new Date(c).toLocaleDateString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),_=(c,h)=>c.length===0?e.jsxs("div",{className:s.emptyState,children:[e.jsx("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19Z"})}),e.jsxs("p",{children:["暂无",h==="bookmarks"?"书签":h==="favorites"?"收藏":"历史记录"]})]}):e.jsx("div",{className:s.chapterList,children:c.map(d=>e.jsxs("div",{className:s.chapterItem,children:[e.jsxs("div",{className:s.chapterInfo,onClick:()=>y(d),children:[e.jsx("h4",{className:s.chapterItemTitle,children:d.title}),e.jsxs("div",{className:s.chapterMeta,children:[e.jsx("span",{className:s.siteName,children:d.site}),e.jsx("span",{className:s.timestamp,children:f(d.timestamp)})]}),d.readingProgress>0&&e.jsxs("div",{className:s.readingProgress,children:[e.jsx("div",{className:s.progressBar,style:{width:`${d.readingProgress}%`}}),e.jsxs("span",{className:s.progressText,children:[Math.round(d.readingProgress),"%"]})]})]}),e.jsxs("div",{className:s.chapterActions,children:[h!=="favorites"&&e.jsx("button",{className:`${s.actionButton} ${o.some(m=>m.id===d.id)?s.favorited:""}`,onClick:()=>x(d),title:o.some(m=>m.id===d.id)?"取消收藏":"添加收藏",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z"})})}),h==="bookmarks"&&e.jsx("button",{className:s.actionButton,onClick:()=>r(d.id),title:"删除书签",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,6.41L17.59,5 12,10.59 6.41,5 5,6.41 10.59,12 5,17.59 6.41,19 12,13.41 17.59,19 19,17.59 13.41,12z"})})}),h==="favorites"&&e.jsx("button",{className:s.actionButton,onClick:()=>a(d.id),title:"取消收藏",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,6.41L17.59,5 12,10.59 6.41,5 5,6.41 10.59,12 5,17.59 6.41,19 12,13.41 17.59,19 19,17.59 13.41,12z"})})})]})]},d.id))});return e.jsxs("div",{className:s.sidePanel,children:[e.jsxs("div",{className:s.panelHeader,children:[e.jsx("h3",{children:"阅读记录"}),e.jsx("button",{className:s.closeButton,onClick:g,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,6.41L17.59,5 12,10.59 6.41,5 5,6.41 10.59,12 5,17.59 6.41,19 12,13.41 17.59,19 19,17.59 13.41,12z"})})})]}),e.jsxs("div",{className:s.panelTabs,children:[e.jsxs("button",{className:`${s.tabButton} ${v==="bookmarks"?s.active:""}`,onClick:()=>u("bookmarks"),children:["书签 (",t.length,")"]}),e.jsxs("button",{className:`${s.tabButton} ${v==="favorites"?s.active:""}`,onClick:()=>u("favorites"),children:["收藏 (",o.length,")"]}),e.jsxs("button",{className:`${s.tabButton} ${v==="history"?s.active:""}`,onClick:()=>u("history"),children:["历史 (",n.length,")"]})]}),e.jsxs("div",{className:s.panelContent,children:[v==="bookmarks"&&_(t,"bookmarks"),v==="favorites"&&_(o,"favorites"),v==="history"&&_(n.slice(0,50),"history")]})]})},jt=({loadingNext:t,loadingPrev:o,onNext:n,onPrev:r,onAddBookmark:a})=>{const{currentChapter:i,toggleBookmarks:l}=w();return e.jsxs("nav",{className:s.chapterNavigation,children:[e.jsxs("button",{className:s.navButton,onClick:r,disabled:o||!(i!=null&&i.prevChapterUrl),title:"上一章 (←)",children:[o?e.jsx("div",{className:s.loadingSpinner}):e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"})}),"上一章"]}),e.jsxs("div",{className:s.navControls,children:[e.jsx("button",{className:s.controlButton,onClick:a,title:"添加书签 (Ctrl+B)",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17,18L12,15.82L7,18V5H17M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z"})})}),e.jsx("button",{className:s.controlButton,onClick:l,title:"书签目录",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M3,13H15V11H3M3,6V8H21V6M3,18H9V16H3V18Z"})})}),e.jsxs("div",{className:s.progressIndicator,children:[e.jsx("div",{className:s.progressDot}),e.jsxs("div",{className:s.progressText,children:[Math.round(w.getState().readingPosition*100),"%"]})]}),e.jsx("button",{className:s.controlButton,onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),title:"回到顶部",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z"})})}),e.jsx("button",{className:s.controlButton,onClick:()=>window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"}),title:"回到底部",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"})})})]}),e.jsxs("button",{className:`${s.navButton} ${s.primary}`,onClick:n,disabled:t||!(i!=null&&i.nextChapterUrl),title:"下一章 (→ 或 空格)",children:["下一章",t?e.jsx("div",{className:s.loadingSpinner}):e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"})})]})]})},wt="nr__floatingContainer__E1efY",kt="nr__animate__pchya",Nt="nr__floatingButton__1WRBT",Bt="nr__buttonContent__10ygT",Lt="nr__buttonText__OTGS0",Et="nr__pulseRing__BvWya",Rt="nr__pulse__glFNx",Tt="nr__tooltip__Q3hbD",It="nr__tooltipArrow__kNIL1",C={floatingContainer:wt,animate:kt,floatingButton:Nt,buttonContent:Bt,buttonText:Lt,pulseRing:Et,pulse:Rt,tooltip:Tt,tooltipArrow:It},Mt=({onNext:t,onPrev:o,onAddBookmark:n})=>{const[r,a]=j.useState(!1),i=()=>{a(!r)};return e.jsxs("div",{className:`${C.floatingControls} ${r?C.expanded:""}`,children:[e.jsx("button",{className:C.mainButton,onClick:i,title:r?"收起":"展开控制",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",style:{transform:r?"rotate(45deg)":"rotate(0deg)"},children:e.jsx("path",{d:"M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"})})}),r&&e.jsxs("div",{className:C.controlButtons,children:[e.jsx("button",{className:C.controlButton,onClick:o,title:"上一章 (←)",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"})})}),e.jsx("button",{className:C.controlButton,onClick:n,title:"添加书签 (Ctrl+B)",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z"})})}),e.jsx("button",{className:C.controlButton,onClick:t,title:"下一章 (→)",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"})})})]})]})},Ht=()=>{const{settings:t,currentChapter:o,isReaderMode:n,showSettings:r,showBookmarks:a,readingPosition:i,exitReaderMode:l,updateReadingPosition:p,toggleSettings:g,toggleBookmarks:v}=w(),{navigateToNext:u,navigateToPrev:y,loadingNext:x,loadingPrev:f,addBookmark:_}=A(),[c,h]=j.useState(0),[d,m]=j.useState(!1),S=j.useCallback(b=>{if(n)switch(b.key){case"Escape":l();break;case"ArrowLeft":b.preventDefault(),y();break;case"ArrowRight":b.preventDefault(),u();break;case" ":b.preventDefault(),u();break;case"b":(b.ctrlKey||b.metaKey)&&(b.preventDefault(),B());break;case"s":(b.ctrlKey||b.metaKey)&&(b.preventDefault(),g());break}},[n,l,u,y,g]),N=j.useCallback(()=>{const b=window.pageYOffset||document.documentElement.scrollTop,L=document.documentElement.scrollHeight-window.innerHeight,D=L>0?b/L:0;h(D),p(D),m(b>200)},[p]),B=j.useCallback(()=>{o&&(_(o,i),At("书签已添加"))},[o,i,_]);if(j.useEffect(()=>{if(n){document.addEventListener("keydown",S),window.addEventListener("scroll",N,{passive:!0});const b=L=>{["ArrowLeft","ArrowRight"," "].includes(L.key)&&L.preventDefault()};return document.addEventListener("keydown",b),()=>{document.removeEventListener("keydown",S),window.removeEventListener("scroll",N),document.removeEventListener("keydown",b)}}},[n,S,N]),!n||!o)return null;const Z={light:s.lightTheme,dark:s.darkTheme,sepia:s.sepiaTheme}[t.theme];return e.jsxs("div",{className:`${s.readerContainer} ${Z}`,style:{"--reader-font-size":`${t.fontSize}px`,"--reader-font-family":t.fontFamily,"--reader-line-height":t.lineHeight,"--reader-page-width":`${t.pageWidth}px`},children:[e.jsx("div",{className:s.progressBar,children:e.jsx("div",{className:s.progressFill,style:{width:`${c*100}%`}})}),e.jsxs("header",{className:s.readerHeader,children:[e.jsx("div",{className:s.headerLeft,children:e.jsx("button",{className:s.exitButton,onClick:l,title:"退出阅读模式 (ESC)",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})})}),e.jsx("h1",{className:s.chapterTitle,title:o.title,children:o.title}),e.jsxs("div",{className:s.headerRight,children:[e.jsx("button",{className:`${s.headerButton} ${a?s.active:""}`,onClick:v,title:"书签",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z"})})}),e.jsx("button",{className:`${s.headerButton} ${r?s.active:""}`,onClick:g,title:"设置 (Ctrl+S)",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"})})})]})]}),e.jsx("main",{className:s.readerMain,children:e.jsx("div",{className:s.readerContent,children:o.isPaid&&!o.hasAccess?e.jsxs("div",{className:s.paywallNotice,children:[e.jsx("div",{className:s.paywallIcon,children:e.jsx("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"})})}),e.jsx("h3",{children:"此章节需要付费阅读"}),e.jsx("p",{children:"请前往原网站完成付费后继续阅读"}),e.jsx("button",{className:s.paywallButton,onClick:()=>window.open(o.url,"_blank"),children:"前往付费"})]}):e.jsxs(e.Fragment,{children:[e.jsx("article",{className:s.chapterContent,dangerouslySetInnerHTML:{__html:o.content}}),e.jsxs("aside",{className:s.additionalContent,children:[o.authorInfo&&e.jsx("div",{className:s.authorInfo,dangerouslySetInnerHTML:{__html:o.authorInfo}}),o.comments&&e.jsx("div",{className:s.commentsSection,dangerouslySetInnerHTML:{__html:o.comments}})]})]})})}),r&&e.jsx(bt,{}),a&&e.jsx(Ct,{}),e.jsx(jt,{loadingNext:x,loadingPrev:f,onNext:u,onPrev:y,onAddBookmark:B}),d&&e.jsx(Mt,{onNext:u,onPrev:y,onAddBookmark:B})]})};function At(t){const o=document.createElement("div");o.className=s.notification,o.textContent=t,document.body.appendChild(o),setTimeout(()=>{o.style.opacity="0",setTimeout(()=>{document.body.removeChild(o)},300)},2e3)}const Dt=({onClick:t,isVisible:o})=>{const[n,r]=j.useState(!1),[a,i]=j.useState(!1);return j.useEffect(()=>{if(o){const l=setTimeout(()=>{r(!0),i(!0)},1e3);return()=>clearTimeout(l)}else r(!1),i(!1)},[o]),n?e.jsxs("div",{className:`${C.floatingContainer} ${a?C.animate:""}`,children:[e.jsxs("button",{className:C.floatingButton,onClick:t,title:"进入阅读模式",children:[e.jsxs("div",{className:C.buttonContent,children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M17,17H7V16H17V17M17,15H7V14H17V15M17,13H7V12H17V13M17,11H7V10H17V11M17,9H7V8H17V9Z"})}),e.jsx("span",{className:C.buttonText,children:"阅读模式"})]}),e.jsx("div",{className:C.pulseRing}),e.jsx("div",{className:C.pulseRing,style:{animationDelay:"1s"}})]}),e.jsxs("div",{className:C.tooltip,children:["点击进入沉浸式阅读模式",e.jsx("div",{className:C.tooltipArrow})]})]}):null},Pt={"jjwxc.net":{domain:"jjwxc.net",name:"晋江文学城",selectors:{title:".noveltext h2, .readtitle, h1",content:".noveltext, .readtext, .text",nextChapter:'a[title*="下一章"], .nextpage, .next',prevChapter:'a[title*="上一章"], .prevpage, .prev',comments:".comment-area, .pl, .review",authorInfo:".author-info, .readsmall, .author",paywall:".vip-content, .buyvip, .pay-chapter"},contentProcessor:t=>{const o=t.cloneNode(!0);o.querySelectorAll(".ad, .advertisement, .sponsor").forEach(r=>r.remove()),o.querySelectorAll("script, style").forEach(r=>r.remove());let n=o.innerHTML;return n=n.replace(/<br\s*\/?>/gi,`
`),n=n.replace(/\n\s*\n/g,`

`),n},urlProcessor:t=>t.startsWith("/")?`https://${window.location.hostname}${t}`:t},"qidian.com":{domain:"qidian.com",name:"起点中文网",selectors:{title:".chapter-title, .read-title, h3.title",content:".read-content, .chapter-content, .text-content",nextChapter:".chapter-control .next, .read-nav .next",prevChapter:".chapter-control .prev, .read-nav .prev",comments:".chapter-review, .comment-wrap",authorInfo:".author-intro, .book-info",paywall:".vip-chapter, .pay-chapter"},contentProcessor:t=>{const o=t.cloneNode(!0);o.querySelectorAll(".ad, .qd_om, .volume-wrap").forEach(r=>r.remove());let n=o.innerHTML;return n=n.replace(/<p[^>]*>/gi,"<p>"),n=n.replace(/&nbsp;/g," "),n}},"zongheng.com":{domain:"zongheng.com",name:"纵横中文网",selectors:{title:".title, .chapter_title, h1",content:".content, .chapter_content, .readcontent",nextChapter:".page_chapter .next, .readpage .next",prevChapter:".page_chapter .prev, .readpage .prev",comments:".book-comment, .chapter-comment",authorInfo:".author-info, .bookinfo",paywall:".vip-info, .pay-info"},contentProcessor:t=>{const o=t.cloneNode(!0);return o.querySelectorAll(".ad, .float-ad, .chapter-ad").forEach(n=>n.remove()),o.innerHTML}}};function Ft(){const t=window.location.hostname;for(const o of Object.values(Pt))if(t.includes(o.domain))return o;return null}async function Ot(t,o=document){var n,r,a;try{const i=o.querySelector(t.selectors.title),l=((n=i==null?void 0:i.textContent)==null?void 0:n.trim())||"未知章节",p=o.querySelector(t.selectors.content);if(!p)throw new Error("无法找到章节内容");let g=t.contentProcessor?t.contentProcessor(p):p.innerHTML;g=Vt(g);const u=!!(t.selectors.paywall?o.querySelector(t.selectors.paywall):null),y=!u||$t(o),x=o.querySelector(t.selectors.nextChapter),f=o.querySelector(t.selectors.prevChapter),_=x!=null&&x.href?t.urlProcessor?t.urlProcessor(x.href):x.href:void 0,c=f!=null&&f.href?t.urlProcessor?t.urlProcessor(f.href):f.href:void 0,h=t.selectors.authorInfo?(r=o.querySelector(t.selectors.authorInfo))==null?void 0:r.outerHTML:void 0,d=t.selectors.comments?(a=o.querySelector(t.selectors.comments))==null?void 0:a.outerHTML:void 0;return{id:zt(window.location.href),title:l,content:g,url:window.location.href,isPaid:u,hasAccess:y,nextChapterUrl:_,prevChapterUrl:c,authorInfo:h,comments:d}}catch(i){return console.error("提取章节内容失败:",i),null}}function Vt(t){return t=t.replace(/<script[^>]*>[\s\S]*?<\/script>/gi,""),t=t.replace(/<style[^>]*>[\s\S]*?<\/style>/gi,""),t=t.replace(/<!--[\s\S]*?-->/g,""),t=t.replace(/\s+/g," "),t=t.replace(/>\s+</g,"><"),t=t.replace(/<p[^>]*>\s*<\/p>/gi,""),t=t.replace(/<div[^>]*>\s*<\/div>/gi,""),t=t.replace(/<p[^>]*>/gi,"<p>"),t=t.replace(/\n\s*\n/g,`

`),t.trim()}function $t(t){var n;const o=[".vip-content",".pay-chapter",".buyvip",".need-pay"];for(const r of o){const a=t.querySelector(r);if(a&&(((n=a.textContent)==null?void 0:n.trim())||"").length<100)return!1}return!0}function zt(t){return btoa(encodeURIComponent(t)).replace(/[^a-zA-Z0-9]/g,"").slice(0,16)}class Ut{constructor(){this.root=null,this.container=null,this.floatingButton=null,this.siteConfig=null,this.init()}async init(){this.siteConfig=Ft(),this.siteConfig&&(document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>this.setup()):this.setup())}setup(){this.isNovelContentPage()&&(this.createFloatingButton(),this.createReactContainer(),this.observePageChanges())}isNovelContentPage(){if(!this.siteConfig)return!1;const o=document.querySelector(this.siteConfig.selectors.title),n=document.querySelector(this.siteConfig.selectors.content);return!!(o&&n)}createFloatingButton(){this.floatingButton&&this.floatingButton.remove();const o=document.createElement("div");o.id="novel-reader-floating-button",o.style.cssText=`
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 999998;
      pointer-events: none;
    `,document.body.appendChild(o),this.floatingButton=o,P(o).render(e.jsx(Dt,{onClick:this.enterReaderMode.bind(this),isVisible:!w.getState().isReaderMode}))}createReactContainer(){this.container||(this.container=document.createElement("div"),this.container.id="novel-reader-extension-root",this.container.style.cssText=`
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999999;
        pointer-events: none;
      `,document.body.appendChild(this.container),this.root=P(this.container),this.root.render(e.jsx(Ht,{})))}async enterReaderMode(){try{const o=await this.extractCurrentChapter();if(!o){console.error("无法提取章节内容");return}w.getState().enterReaderMode(o),A.getState().addToHistory(o),this.floatingButton&&(this.floatingButton.style.display="none"),document.body.style.overflow="hidden"}catch(o){console.error("进入阅读模式失败:",o),this.showErrorMessage("进入阅读模式失败，请稍后重试")}}async extractCurrentChapter(){return this.siteConfig?Ot(this.siteConfig,document):null}observePageChanges(){let o=window.location.href;new MutationObserver(()=>{window.location.href!==o&&(o=window.location.href,setTimeout(()=>{this.isNovelContentPage()?this.createFloatingButton():this.removeFloatingButton()},1e3))}).observe(document.body,{childList:!0,subtree:!0}),window.addEventListener("popstate",()=>{setTimeout(()=>{this.isNovelContentPage()?this.createFloatingButton():this.removeFloatingButton()},500)})}removeFloatingButton(){this.floatingButton&&(this.floatingButton.remove(),this.floatingButton=null)}showErrorMessage(o){const n=document.createElement("div");n.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #ff4757;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      z-index: 1000000;
      box-shadow: 0 4px 20px rgba(255, 71, 87, 0.3);
      animation: slideInRight 0.3s ease;
    `,n.textContent=o,document.body.appendChild(n),setTimeout(()=>{n.style.animation="slideOutRight 0.3s ease",setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n)},300)},3e3)}}chrome.runtime.onMessage.addListener((t,o,n)=>{var r;switch(t.type){case"TOGGLE_READER_MODE":const{isReaderMode:a}=w.getState();if(a)w.getState().exitReaderMode(),document.body.style.overflow="";else{const l=window.novelReaderExtension;l&&l.enterReaderMode()}n({success:!0,isReaderMode:!a});break;case"GET_READING_STATE":const i=w.getState();n({isReaderMode:i.isReaderMode,currentChapter:((r=i.currentChapter)==null?void 0:r.title)||null});break}});w.subscribe(t=>{if(!t.isReaderMode){document.body.style.overflow="";const o=window.novelReaderExtension;o&&o.floatingButton&&(o.floatingButton.style.display="block")}});const qt=new Ut;window.novelReaderExtension=qt;const W=document.createElement("style");W.textContent=`
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;document.head.appendChild(W);
