// scripts/generate-icons.js
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 创建简单的PNG图标数据（base64编码的1x1像素PNG）
const createSimplePNG = (size, color = '#667eea') => {
  // 这是一个简化的方法，创建一个纯色的PNG
  // 在实际项目中，你可能想要使用更复杂的图标生成
  const canvas = `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 书本背景 -->
  <rect x="${size * 0.15}" y="${size * 0.125}" width="${size * 0.7}" height="${size * 0.75}" rx="${size * 0.06}" ry="${size * 0.06}" fill="url(#bookGradient)" />
  
  <!-- 书本边框 -->
  <rect x="${size * 0.15}" y="${size * 0.125}" width="${size * 0.7}" height="${size * 0.75}" rx="${size * 0.06}" ry="${size * 0.06}" fill="none" stroke="#4a5568" stroke-width="${Math.max(1, size * 0.015)}" />
  
  <!-- 书脊 -->
  <rect x="${size * 0.15}" y="${size * 0.125}" width="${size * 0.09}" height="${size * 0.75}" rx="${size * 0.06}" ry="${size * 0.06}" fill="#4a5568" />
  
  <!-- 页面线条 -->
  <line x1="${size * 0.31}" y1="${size * 0.25}" x2="${size * 0.72}" y2="${size * 0.25}" stroke="white" stroke-width="${Math.max(1, size * 0.015)}" opacity="0.8" />
  <line x1="${size * 0.31}" y1="${size * 0.34}" x2="${size * 0.72}" y2="${size * 0.34}" stroke="white" stroke-width="${Math.max(1, size * 0.015)}" opacity="0.8" />
  <line x1="${size * 0.31}" y1="${size * 0.44}" x2="${size * 0.72}" y2="${size * 0.44}" stroke="white" stroke-width="${Math.max(1, size * 0.015)}" opacity="0.8" />
  <line x1="${size * 0.31}" y1="${size * 0.53}" x2="${size * 0.62}" y2="${size * 0.53}" stroke="white" stroke-width="${Math.max(1, size * 0.015)}" opacity="0.8" />
  <line x1="${size * 0.31}" y1="${size * 0.62}" x2="${size * 0.69}" y2="${size * 0.62}" stroke="white" stroke-width="${Math.max(1, size * 0.015)}" opacity="0.8" />
  <line x1="${size * 0.31}" y1="${size * 0.72}" x2="${size * 0.59}" y2="${size * 0.72}" stroke="white" stroke-width="${Math.max(1, size * 0.015)}" opacity="0.8" />
  
  <!-- 书签 -->
  <rect x="${size * 0.19}" y="${size * 0.09}" width="${size * 0.03}" height="${size * 0.16}" fill="#ffd700" />
  <polygon points="${size * 0.19},${size * 0.25} ${size * 0.22},${size * 0.25} ${size * 0.205},${size * 0.22}" fill="#ffd700" />
</svg>`;
  
  return canvas;
};

// 创建图标目录
const iconsDir = path.join(__dirname, '..', 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// 生成不同尺寸的SVG图标
const sizes = [16, 32, 48, 128];

sizes.forEach(size => {
  const svgContent = createSimplePNG(size);
  fs.writeFileSync(path.join(iconsDir, `icon${size}.svg`), svgContent);
  console.log(`Generated icon${size}.svg`);
});

console.log('Icons generated successfully!');
console.log('Note: These are SVG files. For production, you may want to convert them to PNG files.');
