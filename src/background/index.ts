// src/background/index.ts

// 插件安装/更新时的处理
chrome.runtime.onInstalled.addListener((details) => {
  console.log('小说阅读助手已安装/更新');
  
  if (details.reason === 'install') {
    // 首次安装时显示欢迎页面
    chrome.tabs.create({
      url: chrome.runtime.getURL('welcome.html')
    });
    
    // 设置默认配置
    chrome.storage.sync.set({
      settings: {
        fontSize: 16,
        fontFamily: 'Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif',
        lineHeight: 1.8,
        theme: 'light',
        pageWidth: 800,
        autoScroll: false,
        scrollSpeed: 50
      },
      enabledSites: ['jjwxc.net', 'qidian.com', 'zongheng.com'],
      showFloatingButton: true,
      keyboardShortcuts: true
    });
  }
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'GET_SETTINGS':
      // 获取用户设置
      chrome.storage.sync.get(['settings'], (result) => {
        sendResponse({ settings: result.settings });
      });
      return true; // 保持消息通道开放
      
    case 'SAVE_SETTINGS':
      // 保存用户设置
      chrome.storage.sync.set({ settings: message.settings }, () => {
        sendResponse({ success: true });
        
        // 通知所有标签页设置已更新
        chrome.tabs.query({}, (tabs) => {
          tabs.forEach(tab => {
            if (tab.id) {
              chrome.tabs.sendMessage(tab.id, {
                type: 'SETTINGS_UPDATED',
                settings: message.settings
              }).catch(() => {
                // 忽略错误，可能是非内容页面
              });
            }
          });
        });
      });
      return true;
      
    case 'ADD_BOOKMARK':
      // 添加书签
      handleAddBookmark(message.bookmark, sendResponse);
      return true;
      
    case 'GET_BOOKMARKS':
      // 获取书签列表
      handleGetBookmarks(sendResponse);
      return true;
      
    case 'REMOVE_BOOKMARK':
      // 删除书签
      handleRemoveBookmark(message.bookmarkId, sendResponse);
      return true;
      
    case 'GET_READING_HISTORY':
      // 获取阅读历史
      handleGetHistory(sendResponse);
      return true;
      
    case 'CLEAR_READING_HISTORY':
      // 清空阅读历史
      handleClearHistory(sendResponse);
      return true;
      
    case 'REPORT_ERROR':
      // 错误报告
      console.error('Content Script Error:', message.error);
      // 这里可以添加错误上报逻辑
      break;
  }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 检查是否是支持的网站
    const supportedSites = ['jjwxc.net', 'qidian.com', 'zongheng.com'];
    const isSupported = supportedSites.some(site => tab.url!.includes(site));
    
    if (isSupported) {
      // 更新插件图标状态
      chrome.action.setIcon({
        tabId: tabId,
        path: {
          16: 'icons/icon16.svg',
          32: 'icons/icon32.svg',
          48: 'icons/icon48.svg',
          128: 'icons/icon128.svg'
        }
      });
      
      chrome.action.setTitle({
        tabId: tabId,
        title: '小说阅读助手 - 点击启用阅读模式'
      });
    } else {
      // 恢复默认图标
      chrome.action.setIcon({
        tabId: tabId,
        path: {
          16: 'icons/icon16.svg',
          32: 'icons/icon32.svg',
          48: 'icons/icon48.svg',
          128: 'icons/icon128.svg'
        }
      });
      
      chrome.action.setTitle({
        tabId: tabId,
        title: '小说阅读助手 - 不支持当前网站'
      });
    }
  }
});

// 监听快捷键命令
chrome.commands.onCommand.addListener((command, tab) => {
  if (command === 'toggle-reader-mode' && tab.id) {
    chrome.tabs.sendMessage(tab.id, {
      type: 'TOGGLE_READER_MODE'
    });
  }
});

// 书签管理功能
async function handleAddBookmark(bookmark: any, sendResponse: (response: any) => void) {
  try {
    const result = await chrome.storage.sync.get(['bookmarks']);
    const bookmarks = result.bookmarks || [];
    
    // 检查是否已存在相同书签
    const exists = bookmarks.some((b: any) => b.chapterUrl === bookmark.chapterUrl);
    if (exists) {
      sendResponse({ success: false, error: '书签已存在' });
      return;
    }
    
    bookmarks.unshift({
      ...bookmark,
      id: generateId(),
      createdAt: Date.now()
    });
    
    // 限制书签数量
    if (bookmarks.length > 100) {
      bookmarks.splice(100);
    }
    
    await chrome.storage.sync.set({ bookmarks });
    sendResponse({ success: true });
    
  } catch (error) {
    console.error('添加书签失败:', error);
    sendResponse({ success: false, error: '添加书签失败' });
  }
}

async function handleGetBookmarks(sendResponse: (response: any) => void) {
  try {
    const result = await chrome.storage.sync.get(['bookmarks']);
    sendResponse({ bookmarks: result.bookmarks || [] });
  } catch (error) {
    console.error('获取书签失败:', error);
    sendResponse({ bookmarks: [] });
  }
}

async function handleRemoveBookmark(bookmarkId: string, sendResponse: (response: any) => void) {
  try {
    const result = await chrome.storage.sync.get(['bookmarks']);
    const bookmarks = result.bookmarks || [];
    
    const filteredBookmarks = bookmarks.filter((b: any) => b.id !== bookmarkId);
    await chrome.storage.sync.set({ bookmarks: filteredBookmarks });
    
    sendResponse({ success: true });
  } catch (error) {
    console.error('删除书签失败:', error);
    sendResponse({ success: false, error: '删除书签失败' });
  }
}

// 历史记录管理
async function handleGetHistory(sendResponse: (response: any) => void) {
  try {
    const result = await chrome.storage.local.get(['readingHistory']);
    sendResponse({ history: result.readingHistory || [] });
  } catch (error) {
    console.error('获取历史记录失败:', error);
    sendResponse({ history: [] });
  }
}

async function handleClearHistory(sendResponse: (response: any) => void) {
  try {
    await chrome.storage.local.set({ readingHistory: [] });
    sendResponse({ success: true });
  } catch (error) {
    console.error('清空历史记录失败:', error);
    sendResponse({ success: false, error: '清空历史记录失败' });
  }
}

// 工具函数
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'sync' && changes.settings) {
    // 设置变更时通知所有内容脚本
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        if (tab.id) {
          chrome.tabs.sendMessage(tab.id, {
            type: 'SETTINGS_UPDATED',
            settings: changes.settings.newValue
          }).catch(() => {
            // 忽略错误
          });
        }
      });
    });
  }
});

// 定期清理过期数据
chrome.alarms.create('cleanup', { periodInMinutes: 60 * 24 }); // 每天清理一次

chrome.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'cleanup') {
    try {
      // 清理30天前的历史记录
      const result = await chrome.storage.local.get(['readingHistory']);
      const history = result.readingHistory || [];
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      
      const filteredHistory = history.filter((item: any) => item.visitedAt > thirtyDaysAgo);
      
      if (filteredHistory.length !== history.length) {
        await chrome.storage.local.set({ readingHistory: filteredHistory });
        console.log(`清理了 ${history.length - filteredHistory.length} 条过期历史记录`);
      }
    } catch (error) {
      console.error('清理数据失败:', error);
    }
  }
});

// 处理插件卸载
chrome.runtime.setUninstallURL('https://forms.gle/feedback-uninstall');

console.log('小说阅读助手后台脚本已启动');