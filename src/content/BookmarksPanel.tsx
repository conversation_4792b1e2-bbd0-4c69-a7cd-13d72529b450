// src/content/BookmarksPanel.tsx
import React, { useState } from "react";
import { useChaptersStore } from "@/stores/chaptersStore";
import { useReaderStore } from "@/stores/readerStore";
import styles from "./reader.module.css";

const BookmarksPanel: React.FC = () => {
  const {
    bookmarks,
    favorites,
    history,
    removeBookmark,
    removeFromFavorites,
    addToFavorites,
    addBookmark,
  } = useChaptersStore();

  const { loadChapter, toggleBookmarks } = useReaderStore();
  const [activeTab, setActiveTab] = useState<'bookmarks' | 'favorites' | 'history'>('bookmarks');

  const handleChapterClick = (chapter: any) => {
    loadChapter(chapter);
    toggleBookmarks(); // 关闭面板
  };

  const handleToggleFavorite = (chapter: any) => {
    const isFavorite = favorites.some(f => f.id === chapter.id);
    if (isFavorite) {
      removeFromFavorites(chapter.id);
    } else {
      addToFavorites(chapter);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderChapterList = (chapters: any[], type: string) => {
    if (chapters.length === 0) {
      return (
        <div className={styles.emptyState}>
          <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19Z" />
          </svg>
          <p>暂无{type === 'bookmarks' ? '书签' : type === 'favorites' ? '收藏' : '历史记录'}</p>
        </div>
      );
    }

    return (
      <div className={styles.chapterList}>
        {chapters.map((chapter) => (
          <div key={chapter.id} className={styles.chapterItem}>
            <div className={styles.chapterInfo} onClick={() => handleChapterClick(chapter)}>
              <h4 className={styles.chapterItemTitle}>{chapter.title}</h4>
              <div className={styles.chapterMeta}>
                <span className={styles.siteName}>{chapter.site}</span>
                <span className={styles.timestamp}>{formatDate(chapter.timestamp)}</span>
              </div>
              {chapter.readingProgress > 0 && (
                <div className={styles.readingProgress}>
                  <div 
                    className={styles.progressBar}
                    style={{ width: `${chapter.readingProgress}%` }}
                  />
                  <span className={styles.progressText}>{Math.round(chapter.readingProgress)}%</span>
                </div>
              )}
            </div>
            
            <div className={styles.chapterActions}>
              {type !== 'favorites' && (
                <button
                  className={`${styles.actionButton} ${
                    favorites.some(f => f.id === chapter.id) ? styles.favorited : ''
                  }`}
                  onClick={() => handleToggleFavorite(chapter)}
                  title={favorites.some(f => f.id === chapter.id) ? '取消收藏' : '添加收藏'}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5 2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z" />
                  </svg>
                </button>
              )}
              
              {type === 'bookmarks' && (
                <button
                  className={styles.actionButton}
                  onClick={() => removeBookmark(chapter.id)}
                  title="删除书签"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19,6.41L17.59,5 12,10.59 6.41,5 5,6.41 10.59,12 5,17.59 6.41,19 12,13.41 17.59,19 19,17.59 13.41,12z" />
                  </svg>
                </button>
              )}
              
              {type === 'favorites' && (
                <button
                  className={styles.actionButton}
                  onClick={() => removeFromFavorites(chapter.id)}
                  title="取消收藏"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19,6.41L17.59,5 12,10.59 6.41,5 5,6.41 10.59,12 5,17.59 6.41,19 12,13.41 17.59,19 19,17.59 13.41,12z" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={styles.sidePanel}>
      <div className={styles.panelHeader}>
        <h3>阅读记录</h3>
        <button className={styles.closeButton} onClick={toggleBookmarks}>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,6.41L17.59,5 12,10.59 6.41,5 5,6.41 10.59,12 5,17.59 6.41,19 12,13.41 17.59,19 19,17.59 13.41,12z" />
          </svg>
        </button>
      </div>

      <div className={styles.panelTabs}>
        <button
          className={`${styles.tabButton} ${activeTab === 'bookmarks' ? styles.active : ''}`}
          onClick={() => setActiveTab('bookmarks')}
        >
          书签 ({bookmarks.length})
        </button>
        <button
          className={`${styles.tabButton} ${activeTab === 'favorites' ? styles.active : ''}`}
          onClick={() => setActiveTab('favorites')}
        >
          收藏 ({favorites.length})
        </button>
        <button
          className={`${styles.tabButton} ${activeTab === 'history' ? styles.active : ''}`}
          onClick={() => setActiveTab('history')}
        >
          历史 ({history.length})
        </button>
      </div>

      <div className={styles.panelContent}>
        {activeTab === 'bookmarks' && renderChapterList(bookmarks, 'bookmarks')}
        {activeTab === 'favorites' && renderChapterList(favorites, 'favorites')}
        {activeTab === 'history' && renderChapterList(history.slice(0, 50), 'history')}
      </div>
    </div>
  );
};

export default BookmarksPanel;
