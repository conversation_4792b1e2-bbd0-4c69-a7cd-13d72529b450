// src/content/FloatingControls.tsx
import React, { useState } from "react";
import styles from "./floating.module.css";

interface FloatingControlsProps {
  onNext: () => void;
  onPrev: () => void;
  onAddBookmark: () => void;
}

const FloatingControls: React.FC<FloatingControlsProps> = ({
  onNext,
  onPrev,
  onAddBookmark,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={`${styles.floatingControls} ${isExpanded ? styles.expanded : ''}`}>
      {/* 主按钮 */}
      <button
        className={styles.mainButton}
        onClick={toggleExpanded}
        title={isExpanded ? "收起" : "展开控制"}
      >
        <svg 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="currentColor"
          style={{ transform: isExpanded ? 'rotate(45deg)' : 'rotate(0deg)' }}
        >
          <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
        </svg>
      </button>

      {/* 控制按钮组 */}
      {isExpanded && (
        <div className={styles.controlButtons}>
          <button
            className={styles.controlButton}
            onClick={onPrev}
            title="上一章 (←)"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z" />
            </svg>
          </button>

          <button
            className={styles.controlButton}
            onClick={onAddBookmark}
            title="添加书签 (Ctrl+B)"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3Z" />
            </svg>
          </button>

          <button
            className={styles.controlButton}
            onClick={onNext}
            title="下一章 (→)"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default FloatingControls;
