/* src/content/content.css */

/* 全局重置样式 */
#novel-reader-extension-root * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 浮动按钮样式 */
#novel-reader-floating-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 999998;
  pointer-events: auto;
}

/* 阅读模式容器 */
#novel-reader-extension-root {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
  pointer-events: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 当阅读模式激活时，允许指针事件 */
#novel-reader-extension-root.active {
  pointer-events: auto;
}

/* 阅读模式背景 */
.reader-mode-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

/* 阅读器主容器 */
.reader-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

/* 阅读内容区域 */
.reader-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 阅读器头部 */
.reader-header {
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

/* 阅读器标题 */
.reader-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 关闭按钮 */
.reader-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.reader-close-btn:hover {
  background: #e9ecef;
  color: #333;
}

/* 阅读器主体 */
.reader-body {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
}

/* 章节标题 */
.chapter-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
  line-height: 1.4;
}

/* 章节内容 */
.chapter-content {
  font-size: 16px;
  line-height: 1.8;
  color: #444;
  text-align: justify;
  word-break: break-word;
}

.chapter-content p {
  margin-bottom: 16px;
  text-indent: 2em;
}

.chapter-content p:last-child {
  margin-bottom: 0;
}

/* 导航按钮 */
.reader-navigation {
  padding: 20px 30px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.nav-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.nav-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 设置面板 */
.settings-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 20px;
  min-width: 250px;
  z-index: 1000000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reader-container {
    padding: 10px;
  }
  
  .reader-content {
    max-height: 95vh;
    border-radius: 8px;
  }
  
  .reader-header,
  .reader-navigation {
    padding: 15px 20px;
  }
  
  .reader-body {
    padding: 20px;
  }
  
  .chapter-title {
    font-size: 20px;
  }
  
  .chapter-content {
    font-size: 15px;
    line-height: 1.7;
  }
  
  .settings-panel {
    position: fixed;
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
  }
}

/* 滚动条样式 */
.reader-body::-webkit-scrollbar {
  width: 6px;
}

.reader-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.reader-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.reader-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误提示 */
.error-message {
  color: #ff4757;
  text-align: center;
  padding: 20px;
  font-size: 16px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-state svg {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}
