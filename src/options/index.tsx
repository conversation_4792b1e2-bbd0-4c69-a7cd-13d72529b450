// src/options/index.tsx
import React, { useState, useEffect } from "react";
import { createRoot } from "react-dom/client";
import "./options.module.css";

interface Settings {
  fontSize: number;
  lineHeight: number;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  autoScroll: boolean;
  scrollSpeed: number;
  enableKeyboardShortcuts: boolean;
}

const defaultSettings: Settings = {
  fontSize: 16,
  lineHeight: 1.6,
  backgroundColor: "#ffffff",
  textColor: "#333333",
  fontFamily: "system-ui",
  autoScroll: false,
  scrollSpeed: 50,
  enableKeyboardShortcuts: true,
};

const Options: React.FC = () => {
  const [settings, setSettings] = useState<Settings>(defaultSettings);
  const [saved, setSaved] = useState(false);

  useEffect(() => {
    // 加载保存的设置
    chrome.storage.sync.get("readerSettings", (result) => {
      if (result.readerSettings) {
        setSettings({ ...defaultSettings, ...result.readerSettings });
      }
    });
  }, []);

  const saveSettings = () => {
    chrome.storage.sync.set({ readerSettings: settings }, () => {
      setSaved(true);
      setTimeout(() => setSaved(false), 2000);
    });
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
  };

  return (
    <div className="options-container">
      <header className="header">
        <h1>小说阅读助手 - 设置</h1>
        <p>自定义您的阅读体验</p>
      </header>

      <main className="main-content">
        <section className="settings-section">
          <h2>阅读样式</h2>
          
          <div className="setting-item">
            <label htmlFor="fontSize">字体大小</label>
            <input
              type="range"
              id="fontSize"
              min="12"
              max="24"
              value={settings.fontSize}
              onChange={(e) => setSettings({...settings, fontSize: parseInt(e.target.value)})}
            />
            <span>{settings.fontSize}px</span>
          </div>

          <div className="setting-item">
            <label htmlFor="lineHeight">行高</label>
            <input
              type="range"
              id="lineHeight"
              min="1.2"
              max="2.0"
              step="0.1"
              value={settings.lineHeight}
              onChange={(e) => setSettings({...settings, lineHeight: parseFloat(e.target.value)})}
            />
            <span>{settings.lineHeight}</span>
          </div>

          <div className="setting-item">
            <label htmlFor="fontFamily">字体</label>
            <select
              id="fontFamily"
              value={settings.fontFamily}
              onChange={(e) => setSettings({...settings, fontFamily: e.target.value})}
            >
              <option value="system-ui">系统默认</option>
              <option value="serif">宋体</option>
              <option value="sans-serif">黑体</option>
              <option value="monospace">等宽字体</option>
            </select>
          </div>

          <div className="setting-item">
            <label htmlFor="backgroundColor">背景颜色</label>
            <input
              type="color"
              id="backgroundColor"
              value={settings.backgroundColor}
              onChange={(e) => setSettings({...settings, backgroundColor: e.target.value})}
            />
          </div>

          <div className="setting-item">
            <label htmlFor="textColor">文字颜色</label>
            <input
              type="color"
              id="textColor"
              value={settings.textColor}
              onChange={(e) => setSettings({...settings, textColor: e.target.value})}
            />
          </div>
        </section>

        <section className="settings-section">
          <h2>阅读功能</h2>
          
          <div className="setting-item">
            <label htmlFor="autoScroll">自动滚动</label>
            <input
              type="checkbox"
              id="autoScroll"
              checked={settings.autoScroll}
              onChange={(e) => setSettings({...settings, autoScroll: e.target.checked})}
            />
          </div>

          <div className="setting-item">
            <label htmlFor="scrollSpeed">滚动速度</label>
            <input
              type="range"
              id="scrollSpeed"
              min="10"
              max="100"
              value={settings.scrollSpeed}
              onChange={(e) => setSettings({...settings, scrollSpeed: parseInt(e.target.value)})}
            />
            <span>{settings.scrollSpeed}</span>
          </div>

          <div className="setting-item">
            <label htmlFor="enableKeyboardShortcuts">启用键盘快捷键</label>
            <input
              type="checkbox"
              id="enableKeyboardShortcuts"
              checked={settings.enableKeyboardShortcuts}
              onChange={(e) => setSettings({...settings, enableKeyboardShortcuts: e.target.checked})}
            />
          </div>
        </section>

        <div className="actions">
          <button onClick={saveSettings} className="save-btn">
            {saved ? "已保存!" : "保存设置"}
          </button>
          <button onClick={resetSettings} className="reset-btn">
            重置为默认
          </button>
        </div>
      </main>
    </div>
  );
};

// 渲染到页面
const container = document.getElementById("root");
if (container) {
  const root = createRoot(container);
  root.render(<Options />);
}
