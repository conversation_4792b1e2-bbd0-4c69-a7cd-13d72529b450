// src/stores/chaptersStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface ChapterInfo {
  id: string;
  title: string;
  content: string;
  url: string;
  site: string;
  timestamp: number;
  wordCount: number;
  readingProgress: number; // 0-100
  nextChapterUrl?: string;
  prevChapterUrl?: string;
}

export interface ReadingHistory {
  chapters: ChapterInfo[];
  currentIndex: number;
  bookmarks: string[]; // chapter IDs
  favorites: string[]; // chapter IDs
}

interface ChaptersState {
  // 阅读历史
  history: ChapterInfo[];
  currentHistoryIndex: number;
  
  // 书签和收藏
  bookmarks: ChapterInfo[];
  favorites: ChapterInfo[];
  
  // 当前阅读会话
  currentSession: ChapterInfo[];
  currentSessionIndex: number;
  
  // 操作方法
  addToHistory: (chapter: ChapterInfo) => void;
  removeFromHistory: (chapterId: string) => void;
  clearHistory: () => void;

  addBookmark: (chapter: ChapterInfo) => void;
  removeBookmark: (chapterId: string) => void;

  addToFavorites: (chapter: ChapterInfo) => void;
  removeFromFavorites: (chapterId: string) => void;

  // 会话管理
  startNewSession: (chapter: ChapterInfo) => void;
  addToCurrentSession: (chapter: ChapterInfo) => void;
  navigateInSession: (direction: 'prev' | 'next') => ChapterInfo | null;

  // 章节导航
  navigateToNext: () => Promise<void>;
  navigateToPrev: () => Promise<void>;
  loadingNext: boolean;
  loadingPrev: boolean;

  // 阅读进度
  updateReadingProgress: (chapterId: string, progress: number) => void;

  // 搜索和过滤
  searchChapters: (query: string) => ChapterInfo[];
  getChaptersBySite: (site: string) => ChapterInfo[];
  getRecentChapters: (limit?: number) => ChapterInfo[];
}

export const useChaptersStore = create<ChaptersState>()(
  persist(
    (set, get) => ({
      // 初始状态
      history: [],
      currentHistoryIndex: -1,
      bookmarks: [],
      favorites: [],
      currentSession: [],
      currentSessionIndex: -1,
      loadingNext: false,
      loadingPrev: false,

      // 添加到历史记录
      addToHistory: (chapter: ChapterInfo) => {
        set((state) => {
          // 检查是否已存在
          const existingIndex = state.history.findIndex(
            (h) => h.id === chapter.id || h.url === chapter.url
          );

          let newHistory = [...state.history];
          
          if (existingIndex >= 0) {
            // 更新现有章节
            newHistory[existingIndex] = {
              ...newHistory[existingIndex],
              ...chapter,
              timestamp: Date.now(),
            };
          } else {
            // 添加新章节到开头
            newHistory.unshift({
              ...chapter,
              timestamp: Date.now(),
            });
            
            // 限制历史记录数量（最多保留100条）
            if (newHistory.length > 100) {
              newHistory = newHistory.slice(0, 100);
            }
          }

          return {
            history: newHistory,
            currentHistoryIndex: 0,
          };
        });
      },

      // 从历史记录中移除
      removeFromHistory: (chapterId: string) => {
        set((state) => ({
          history: state.history.filter((chapter) => chapter.id !== chapterId),
        }));
      },

      // 清空历史记录
      clearHistory: () => {
        set({
          history: [],
          currentHistoryIndex: -1,
        });
      },

      // 添加书签
      addBookmark: (chapter: ChapterInfo) => {
        set((state) => {
          const exists = state.bookmarks.some((b) => b.id === chapter.id);
          if (exists) return state;

          return {
            bookmarks: [
              {
                ...chapter,
                timestamp: Date.now(),
              },
              ...state.bookmarks,
            ],
          };
        });
      },

      // 移除书签
      removeBookmark: (chapterId: string) => {
        set((state) => ({
          bookmarks: state.bookmarks.filter((b) => b.id !== chapterId),
        }));
      },

      // 添加到收藏
      addToFavorites: (chapter: ChapterInfo) => {
        set((state) => {
          const exists = state.favorites.some((f) => f.id === chapter.id);
          if (exists) return state;

          return {
            favorites: [
              {
                ...chapter,
                timestamp: Date.now(),
              },
              ...state.favorites,
            ],
          };
        });
      },

      // 从收藏中移除
      removeFromFavorites: (chapterId: string) => {
        set((state) => ({
          favorites: state.favorites.filter((f) => f.id !== chapterId),
        }));
      },

      // 开始新的阅读会话
      startNewSession: (chapter: ChapterInfo) => {
        set({
          currentSession: [chapter],
          currentSessionIndex: 0,
        });
      },

      // 添加到当前会话
      addToCurrentSession: (chapter: ChapterInfo) => {
        set((state) => {
          const exists = state.currentSession.some((c) => c.id === chapter.id);
          if (exists) {
            // 如果已存在，更新索引
            const index = state.currentSession.findIndex((c) => c.id === chapter.id);
            return {
              currentSessionIndex: index,
            };
          }

          // 添加到会话末尾
          return {
            currentSession: [...state.currentSession, chapter],
            currentSessionIndex: state.currentSession.length,
          };
        });
      },

      // 在会话中导航
      navigateInSession: (direction: 'prev' | 'next') => {
        const state = get();
        const { currentSession, currentSessionIndex } = state;

        if (currentSession.length === 0) return null;

        let newIndex = currentSessionIndex;
        if (direction === 'prev' && currentSessionIndex > 0) {
          newIndex = currentSessionIndex - 1;
        } else if (direction === 'next' && currentSessionIndex < currentSession.length - 1) {
          newIndex = currentSessionIndex + 1;
        } else {
          return null; // 无法导航
        }

        set({ currentSessionIndex: newIndex });
        return currentSession[newIndex];
      },

      // 导航到下一章
      navigateToNext: async () => {
        // 这里应该实现实际的章节加载逻辑
        // 暂时返回空的Promise
        return Promise.resolve();
      },

      // 导航到上一章
      navigateToPrev: async () => {
        // 这里应该实现实际的章节加载逻辑
        // 暂时返回空的Promise
        return Promise.resolve();
      },

      // 更新阅读进度
      updateReadingProgress: (chapterId: string, progress: number) => {
        set((state) => {
          const updateChapter = (chapters: ChapterInfo[]) =>
            chapters.map((chapter) =>
              chapter.id === chapterId
                ? { ...chapter, readingProgress: Math.max(0, Math.min(100, progress)) }
                : chapter
            );

          return {
            history: updateChapter(state.history),
            bookmarks: updateChapter(state.bookmarks),
            favorites: updateChapter(state.favorites),
            currentSession: updateChapter(state.currentSession),
          };
        });
      },

      // 搜索章节
      searchChapters: (query: string) => {
        const state = get();
        const allChapters = [
          ...state.history,
          ...state.bookmarks,
          ...state.favorites,
        ];

        const uniqueChapters = allChapters.filter(
          (chapter, index, self) =>
            self.findIndex((c) => c.id === chapter.id) === index
        );

        return uniqueChapters.filter(
          (chapter) =>
            chapter.title.toLowerCase().includes(query.toLowerCase()) ||
            chapter.content.toLowerCase().includes(query.toLowerCase())
        );
      },

      // 按网站获取章节
      getChaptersBySite: (site: string) => {
        const state = get();
        return state.history.filter((chapter) => chapter.site === site);
      },

      // 获取最近章节
      getRecentChapters: (limit = 10) => {
        const state = get();
        return state.history
          .sort((a, b) => b.timestamp - a.timestamp)
          .slice(0, limit);
      },
    }),
    {
      name: 'novel-reader-chapters',
      version: 1,
    }
  )
);
