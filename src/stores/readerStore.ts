// src/stores/readerStore.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface ChapterInfo {
  id: string;
  title: string;
  content: string;
  url: string;
  isPaid: boolean;
  hasAccess: boolean;
  nextChapterUrl?: string;
  prevChapterUrl?: string;
  authorInfo?: string;
  comments?: string;
}

export interface ReadingSettings {
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  theme: 'light' | 'dark' | 'sepia';
  pageWidth: number;
  autoScroll: boolean;
  scrollSpeed: number;
}

interface ReaderState {
  // 阅读状态
  isReaderMode: boolean;
  currentChapter: ChapterInfo | null;
  readingPosition: number;
  
  // 用户设置
  settings: ReadingSettings;
  
  // UI状态
  showSettings: boolean;
  showBookmarks: boolean;
  
  // 操作方法
  enterReaderMode: (chapter: ChapterInfo) => void;
  exitReaderMode: () => void;
  updateSettings: (settings: Partial<ReadingSettings>) => void;
  setCurrentChapter: (chapter: ChapterInfo) => void;
  updateReadingPosition: (position: number) => void;
  toggleSettings: () => void;
  toggleBookmarks: () => void;
  loadChapter: (chapter: ChapterInfo) => void;
}

export const useReaderStore = create<ReaderState>()(
  devtools(
    persist(
      (set, get) => ({
        isReaderMode: false,
        currentChapter: null,
        readingPosition: 0,
        showSettings: false,
        showBookmarks: false,
        
        settings: {
          fontSize: 16,
          fontFamily: 'Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif',
          lineHeight: 1.8,
          theme: 'light',
          pageWidth: 800,
          autoScroll: false,
          scrollSpeed: 50
        },
        
        enterReaderMode: (chapter) => {
          set({ 
            isReaderMode: true, 
            currentChapter: chapter,
            showSettings: false,
            showBookmarks: false
          });
        },
        
        exitReaderMode: () => {
          set({ 
            isReaderMode: false,
            showSettings: false,
            showBookmarks: false
          });
        },
        
        updateSettings: (newSettings) => {
          set(state => ({
            settings: { ...state.settings, ...newSettings }
          }));
        },
        
        setCurrentChapter: (chapter) => {
          set({ currentChapter: chapter });
        },
        
        updateReadingPosition: (position) => {
          set({ readingPosition: position });
        },
        
        toggleSettings: () => {
          set(state => ({ 
            showSettings: !state.showSettings,
            showBookmarks: false
          }));
        },
        
        toggleBookmarks: () => {
          set(state => ({
            showBookmarks: !state.showBookmarks,
            showSettings: false
          }));
        },

        loadChapter: (chapter) => {
          set({ currentChapter: chapter });
        }
      }),
      {
        name: 'novel-reader-settings',
        partialize: (state) => ({ settings: state.settings })
      }
    )
  )
);

